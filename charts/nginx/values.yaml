# Default values for nginx.
replicaCount: 1
namespace: ""

image:
  repository: swr.ru-moscow-1.hc.sbercloud.ru/tkp2/sdbp-nginx
  tag: latest
  pullPolicy: Always

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

podAnnotations: {}
podLabels: {}

service:
  type: ClusterIP
  port: 80
  targetPort: 80

# Ingress configuration for virtual hosts
ingress:
  enabled: false

# Configuration for PHP-FPM upstream
phpFpm:
  host: sdbp-php
  port: 9000
  buffers: "16 16k"
  bufferSize: "32k"
  connectTimeout: 300
  sendTimeout: 300
  readTimeout: 300

# Nginx Configuration
config:
  worker_processes: "auto"
  worker_connections: 1024

# Server names configuration
servers:
  admin:
    serverName: ""
  api:
    serverName: ""
  apiMobile:
    serverName: ""
  apiFin:
    serverName: ""

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 50m
    memory: 64Mi

nodeSelector: {}
tolerations: []
affinity: {}
