
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: 54dev-gateway
  namespace: istio-ingressgateway
spec:
  servers:
  - hosts:
    - admin.sdbp.54dev-kube.tkp2.prod
    - api.sdbp.54dev-kube.tkp2.prod
    - api-mobile.sdbp.54dev-kube.tkp2.prod
    - api-mobile.sdbp.an.54dev-kube.tkp2.prod
    - api-fin.sdbp.54dev-kube.tkp2.prod
    port:
      name: http
      number: 80
      protocol: HTTP
    tls: {}
