# Please edit the object below. Lines beginning with a '#' will be ignored,
# and an empty file will abort the edit. If an error occurs while saving this file will be
# reopened with the relevant failures.
#
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: 54dev-gateway
  namespace: 54dev
spec:
  gateways:
  - istio-ingressgateway/54dev-gateway
  hosts:
  - admin.sdbp.54dev-kube.tkp2.prod
  - api.sdbp.54dev-kube.tkp2.prod
  - api-mobile.sdbp.54dev-kube.tkp2.prod
  - api-mobile.sdbp.an.54dev-kube.tkp2.prod
  - api-fin.sdbp.54dev-kube.tkp2.prod
  http:
  - match:
    - authority:
        exact: admin.sdbp.54dev-kube.tkp2.prod
      uri:
        prefix: /
    route:
    - destination:
        host: sdbp-nginx.54dev.svc.cluster.local
        port:
          number: 80
  - match:
    - authority:
        exact: api.sdbp.54dev-kube.tkp2.prod
      uri:
        prefix: /
    route:
    - destination:
        host: sdbp-nginx.54dev.svc.cluster.local
        port:
          number: 80
